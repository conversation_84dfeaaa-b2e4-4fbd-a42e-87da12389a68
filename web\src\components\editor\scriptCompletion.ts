import * as monaco from 'monaco-editor';

// 变量和函数数据接口
interface VariableData {
  id: string;
  key: string;
  path?: string;
  pathDescription?: string;
  description?: string;
  type: string;
  children?: VariableData[];
}

interface FunctionData {
  value: string;
  label: string;
  script: string;
  remark: string;
}

// 全局变量存储
let currentVariables: VariableData[] = [];
let localVariables: VariableData[] = [];
let globalVariables: VariableData[] = [];
let functions: FunctionData[] = [];

// 更新Monaco Editor的类型定义
const updateMonacoTypeDefinitions = () => {
  try {
    // 保持默认的 TypeScript 配置，不禁用原生类型支持
    monaco.languages.typescript.typescriptDefaults.setCompilerOptions({
      target: monaco.languages.typescript.ScriptTarget.ES2015,
      allowNonTsExtensions: true,
      moduleResolution: monaco.languages.typescript.ModuleResolutionKind.NodeJs,
    });

    monaco.languages.typescript.javascriptDefaults.setCompilerOptions({
      target: monaco.languages.typescript.ScriptTarget.ES2015,
      allowNonTsExtensions: true,
      moduleResolution: monaco.languages.typescript.ModuleResolutionKind.NodeJs,
    });

    // 只提供我们自定义对象的类型定义，不干扰原生类型
    const customTypeDefs = `
// 自定义对象类型定义
declare const _data: any;
declare const Utils: any;
`;

    // 清除之前的类型定义
    try {
      monaco.languages.typescript.typescriptDefaults.setExtraLibs([]);
      monaco.languages.typescript.javascriptDefaults.setExtraLibs([]);
    } catch (error) {
      console.warn('清除旧类型定义失败:', error);
    }

    // 添加自定义类型定义到Monaco Editor
    monaco.languages.typescript.typescriptDefaults.addExtraLib(customTypeDefs, 'ts:custom-types.d.ts');
    monaco.languages.typescript.javascriptDefaults.addExtraLib(customTypeDefs, 'ts:custom-types.d.ts');

    console.log('已更新Monaco Editor自定义类型定义');
  } catch (error) {
    console.error('更新Monaco Editor类型定义失败:', error);
  }
};

// 更新智能提示数据的函数
export const updateIntelliSenseData = (data: {
  currentVariables?: VariableData[];
  localVariables?: VariableData[];
  globalVariables?: VariableData[];
  functions?: FunctionData[];
}) => {
  if (data.currentVariables) {
    currentVariables = data.currentVariables;
    console.log('更新当前变量:', currentVariables);
  }
  if (data.localVariables) {
    localVariables = data.localVariables;
    console.log('更新局部变量:', localVariables);
  }
  if (data.globalVariables) {
    globalVariables = data.globalVariables;
    console.log('更新全局变量:', globalVariables);
  }
  if (data.functions) {
    functions = data.functions;
    console.log('更新函数列表:', functions);
  }

  // 更新Monaco Editor的类型定义
  updateMonacoTypeDefinitions();
};

// 扁平化变量列表，生成所有可能的路径
const flattenVariables = (
  variables: VariableData[],
  prefix: string = '',
): Array<{ path: string; variable: VariableData }> => {
  const result: Array<{ path: string; variable: VariableData }> = [];

  variables.forEach((variable) => {
    const currentPath = prefix ? `${prefix}.${variable.key}` : variable.key;
    result.push({ path: currentPath, variable });

    if (variable.children && variable.children.length > 0) {
      result.push(...flattenVariables(variable.children, currentPath));
    }
  });

  return result;
};

// 获取第一级变量建议（只显示第一级，不展平）
const getFirstLevelSuggestions = (variables: VariableData[]): any[] => {
  return variables.map((variable) => {
    // 优先使用中文描述，如果没有则使用类型信息
    const description = variable.description || variable.pathDescription;
    const documentation = description ? `${description} (${variable.type})` : `${variable.type} 类型变量`;

    return {
      label: variable.key,
      kind:
        variable.children && variable.children.length > 0
          ? monaco.languages.CompletionItemKind.Module
          : monaco.languages.CompletionItemKind.Variable,
      insertText: variable.key,
      detail: description || variable.type,
      documentation,
      sortText: `0_${variable.key}`,
    };
  });
};

// 获取函数建议
const getFunctionSuggestions = (): any[] => {
  return functions.map((func) => ({
    label: func.label,
    kind: monaco.languages.CompletionItemKind.Function,
    insertText: func.script,
    detail: func.label,
    documentation: func.remark,
    sortText: `1_${func.label}`,
  }));
};

// 获取内置对象和方法的建议
const getBuiltinSuggestions = (): any[] => {
  return [
    {
      label: 'Utils',
      kind: monaco.languages.CompletionItemKind.Module,
      insertText: 'Utils.',
      detail: '工具类',
      documentation: '包含各种实用函数的工具类',
      sortText: '2_Utils',
    },
    {
      label: '_data',
      kind: monaco.languages.CompletionItemKind.Variable,
      insertText: '_data.',
      detail: '数据对象',
      documentation: '包含局部变量和全局变量的数据对象',
      sortText: '0__data',
    },
    {
      label: 'console.log',
      kind: monaco.languages.CompletionItemKind.Function,
      insertText: 'console.log($1)',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: '控制台输出',
      documentation: '在控制台输出信息',
      sortText: '3_console',
    },
    {
      label: 'JSON.parse',
      kind: monaco.languages.CompletionItemKind.Function,
      insertText: 'JSON.parse($1)',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: 'JSON解析',
      documentation: '解析JSON字符串',
      sortText: '3_JSON',
    },
    {
      label: 'JSON.stringify',
      kind: monaco.languages.CompletionItemKind.Function,
      insertText: 'JSON.stringify($1)',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      detail: 'JSON序列化',
      documentation: '将对象序列化为JSON字符串',
      sortText: '3_JSON',
    },
  ];
};

// 查找嵌套变量的子属性
const findNestedVariable = (variables: VariableData[], path: string): VariableData | null => {
  const pathParts = path.split('.');
  console.log('查找嵌套变量:', { path, pathParts });

  for (const variable of variables) {
    if (variable.key === pathParts[0]) {
      if (pathParts.length === 1) {
        console.log('找到目标变量:', variable.key, '子属性数量:', variable.children?.length || 0);
        return variable;
      }
      // 递归查找子属性
      if (variable.children && variable.children.length > 0) {
        const remainingPath = pathParts.slice(1).join('.');
        return findNestedVariable(variable.children, remainingPath);
      }
    }
  }
  return null;
};

// 获取嵌套对象的子属性建议
const getNestedSuggestions = (variables: VariableData[], path: string): any[] => {
  const variable = findNestedVariable(variables, path);
  if (variable && variable.children && variable.children.length > 0) {
    console.log(
      '生成子属性建议:',
      variable.children.map((c) => c.key),
    );
    return variable.children.map((child) => {
      // 优先使用中文描述，如果没有则使用类型信息
      const description = child.description || child.pathDescription;
      const documentation = description ? `${description} (${child.type})` : `${child.type} 类型属性`;

      return {
        label: child.key,
        kind:
          child.children && child.children.length > 0
            ? monaco.languages.CompletionItemKind.Module
            : monaco.languages.CompletionItemKind.Property,
        insertText: child.key,
        detail: description || child.type,
        documentation,
        sortText: `0_${child.key}`,
      };
    });
  }
  return [];
};

// 检查变量的最终类型
const getFinalVariableType = (variables: VariableData[], path: string): string | null => {
  const variable = findNestedVariable(variables, path);
  return variable ? variable.type : null;
};

// 检查是否应该提供自定义建议
const shouldProvideCustomSuggestions = (trimmedText: string, triggerCharacter?: string): boolean => {
  // 如果是通过点号触发的，检查是否是自定义对象
  if (triggerCharacter === '.') {
    // 如果是 _data. 相关的访问，总是提供自定义建议
    if (trimmedText.includes('_data.') || trimmedText.endsWith('_data.')) {
      return true;
    }

    // 如果是 Utils. 相关的访问，提供自定义建议
    if (trimmedText.includes('Utils.') || trimmedText.endsWith('Utils.')) {
      return true;
    }

    // 如果是我们自定义变量的访问（带点号），检查是否是自定义变量
    const customVariableMatch = trimmedText.match(/([a-zA-Z_$][a-zA-Z0-9_$]*(?:\.[a-zA-Z_$][a-zA-Z0-9_$]*)*)\./);
    if (customVariableMatch) {
      const varPath = customVariableMatch[1];
      const rootVarName = varPath.split('.')[0];

      // 检查是否是我们的自定义变量
      const isCustomVariable =
        currentVariables.some((v) => v.key === rootVarName) ||
        localVariables.some((v) => v.key === rootVarName) ||
        globalVariables.some((v) => v.key === rootVarName);

      if (isCustomVariable) {
        // 对于自定义变量，检查最终变量的类型
        const finalType =
          getFinalVariableType(currentVariables, varPath) ||
          getFinalVariableType(localVariables, varPath) ||
          getFinalVariableType(globalVariables, varPath);

        // 如果最终类型是基础类型，检查是否有子属性
        if (finalType && ['string', 'number', 'boolean'].includes(finalType.toLowerCase())) {
          const variable =
            findNestedVariable(currentVariables, varPath) ||
            findNestedVariable(localVariables, varPath) ||
            findNestedVariable(globalVariables, varPath);

          // 如果变量没有子属性，让原生 JS 智能提示处理（提供 string/number/boolean 的方法）
          if (!variable || !variable.children || variable.children.length === 0) {
            console.log('自定义变量最终类型是基础类型且无子属性:', finalType, '让原生 JS 智能提示处理');
            return false;
          }
        }
        return true;
      }
    }
    return false; // 其他点号触发的情况让默认智能提示处理
  }

  // 如果不是点号触发，检查是否在输入自定义变量名
  if (!triggerCharacter) {
    // 检查当前输入是否可能是自定义变量的开始
    const lastWord = trimmedText.split(/\s+/).pop() || '';
    const isTypingCustomVariable =
      currentVariables.some((v) => v.key.startsWith(lastWord)) ||
      localVariables.some((v) => v.key.startsWith(lastWord)) ||
      globalVariables.some((v) => v.key.startsWith(lastWord)) ||
      lastWord === '_data' ||
      lastWord === 'Utils' ||
      lastWord.length === 0; // 空输入时也提供建议

    return isTypingCustomVariable;
  }

  return true;
};

// 去重建议列表
const deduplicateSuggestions = (suggestions: any[]): any[] => {
  const seen = new Set<string>();
  return suggestions.filter((suggestion) => {
    const key = `${suggestion.label}_${suggestion.kind}`;
    if (seen.has(key)) {
      return false;
    }
    seen.add(key);
    return true;
  });
};

// 注册TypeScript/JavaScript智能提示提供者
monaco.languages.registerCompletionItemProvider('typescript', {
  triggerCharacters: ['.', '_'],
  provideCompletionItems: async (model, position, context) => {
    const suggestions: any[] = [];
    const { lineNumber, column } = position;

    // 获取当前行的文本
    const textBeforePointer = model.getValueInRange({
      startLineNumber: lineNumber,
      startColumn: 0,
      endLineNumber: lineNumber,
      endColumn: column,
    });

    // 简化的输入分析 - 检查是否以特定模式结尾
    const trimmedText = textBeforePointer.trim();

    // 获取触发字符
    const triggerCharacter = context.triggerCharacter;

    console.log('智能提示触发:', {
      trimmedText,
      triggerCharacter,
      shouldProvideCustom: shouldProvideCustomSuggestions(trimmedText, triggerCharacter),
    });

    // 只在特定情况下提供自定义建议
    if (shouldProvideCustomSuggestions(trimmedText, triggerCharacter)) {
      // 检查是否是嵌套对象访问（如 _data.someObject.）
      const dataNestedMatch = trimmedText.match(/_data\.([a-zA-Z_$][a-zA-Z0-9_$]*(?:\.[a-zA-Z_$][a-zA-Z0-9_$]*)*)\./);

      if (dataNestedMatch) {
        const nestedPath = dataNestedMatch[1];
        console.log('_data 嵌套路径:', nestedPath);

        // 在局部变量中查找
        const localNestedSuggestions = getNestedSuggestions(localVariables, nestedPath);
        suggestions.push(...localNestedSuggestions);

        // 在全局变量中查找
        const globalNestedSuggestions = getNestedSuggestions(globalVariables, nestedPath);
        suggestions.push(...globalNestedSuggestions);
      }
      // 检查是否是当前变量的嵌套访问（如 someVar.someProperty.）
      else {
        const currentNestedMatch = trimmedText.match(/([a-zA-Z_$][a-zA-Z0-9_$]*(?:\.[a-zA-Z_$][a-zA-Z0-9_$]*)*)\./);

        if (currentNestedMatch) {
          const nestedPath = currentNestedMatch[1];
          console.log('当前变量嵌套路径:', nestedPath);
          const currentNestedSuggestions = getNestedSuggestions(currentVariables, nestedPath);
          suggestions.push(...currentNestedSuggestions);
        }
      }

      // 如果没有找到嵌套建议，使用原有逻辑
      if (suggestions.length === 0) {
        // 如果是在 _data. 后面，提供局部和全局变量建议（只显示第一级）
        if (trimmedText.endsWith('_data.')) {
          // 添加局部变量建议（只显示第一级）
          const localSuggestions = getFirstLevelSuggestions(localVariables);
          suggestions.push(...localSuggestions);

          // 添加全局变量建议（只显示第一级）
          const globalSuggestions = getFirstLevelSuggestions(globalVariables);
          suggestions.push(...globalSuggestions);
        }
        // 如果是在 Utils. 后面，提供函数建议
        else if (trimmedText.endsWith('Utils.')) {
          const functionSuggestions = getFunctionSuggestions();
          suggestions.push(...functionSuggestions);
        }
        // 否则提供自定义变量建议
        else {
          // 添加临时变量建议（不需要 _data 前缀）
          const currentSuggestions = getFirstLevelSuggestions(currentVariables);
          suggestions.push(...currentSuggestions);

          // 添加内置对象建议
          const builtinSuggestions = getBuiltinSuggestions();
          suggestions.push(...builtinSuggestions);

          // 添加函数建议
          const functionSuggestions = getFunctionSuggestions();
          suggestions.push(...functionSuggestions);
        }
      }

      // 去重并返回建议
      const deduplicatedSuggestions = deduplicateSuggestions(suggestions);
      return {
        suggestions: deduplicatedSuggestions,
      };
    }

    // 对于其他情况，返回空建议，让默认的 TypeScript 智能提示处理
    return {
      suggestions: [],
    };
  },
});

// 同样为JavaScript语言注册
monaco.languages.registerCompletionItemProvider('javascript', {
  triggerCharacters: ['.', '_'],
  provideCompletionItems: async (model, position, context) => {
    const suggestions: any[] = [];
    const { lineNumber, column } = position;

    // 获取当前行的文本
    const textBeforePointer = model.getValueInRange({
      startLineNumber: lineNumber,
      startColumn: 0,
      endLineNumber: lineNumber,
      endColumn: column,
    });

    // 简化的输入分析 - 检查是否以特定模式结尾
    const trimmedText = textBeforePointer.trim();

    // 获取触发字符
    const triggerCharacter = context.triggerCharacter;

    // 只在特定情况下提供自定义建议
    if (shouldProvideCustomSuggestions(trimmedText, triggerCharacter)) {
      // 检查是否是嵌套对象访问（如 _data.someObject.）
      const dataNestedMatch = trimmedText.match(/_data\.([a-zA-Z_$][a-zA-Z0-9_$]*(?:\.[a-zA-Z_$][a-zA-Z0-9_$]*)*)\./);
      if (dataNestedMatch) {
        const nestedPath = dataNestedMatch[1];
        // 在局部变量中查找
        const localNestedSuggestions = getNestedSuggestions(localVariables, nestedPath);
        suggestions.push(...localNestedSuggestions);

        // 在全局变量中查找
        const globalNestedSuggestions = getNestedSuggestions(globalVariables, nestedPath);
        suggestions.push(...globalNestedSuggestions);
      }
      // 检查是否是当前变量的嵌套访问（如 someVar.someProperty.）
      else {
        const currentNestedMatch = trimmedText.match(/([a-zA-Z_$][a-zA-Z0-9_$]*(?:\.[a-zA-Z_$][a-zA-Z0-9_$]*)*)\./);
        if (currentNestedMatch) {
          const nestedPath = currentNestedMatch[1];
          const currentNestedSuggestions = getNestedSuggestions(currentVariables, nestedPath);
          suggestions.push(...currentNestedSuggestions);
        }
      }

      // 如果没有找到嵌套建议，使用原有逻辑
      if (suggestions.length === 0) {
        // 如果是在 _data. 后面，提供局部和全局变量建议（只显示第一级）
        if (trimmedText.endsWith('_data.')) {
          // 添加局部变量建议（只显示第一级）
          const localSuggestions = getFirstLevelSuggestions(localVariables);
          suggestions.push(...localSuggestions);

          // 添加全局变量建议（只显示第一级）
          const globalSuggestions = getFirstLevelSuggestions(globalVariables);
          suggestions.push(...globalSuggestions);
        }
        // 如果是在 Utils. 后面，提供函数建议
        else if (trimmedText.endsWith('Utils.')) {
          const functionSuggestions = getFunctionSuggestions();
          suggestions.push(...functionSuggestions);
        }
        // 否则提供自定义变量建议
        else {
          // 添加临时变量建议（不需要 _data 前缀）
          const currentSuggestions = getFirstLevelSuggestions(currentVariables);
          suggestions.push(...currentSuggestions);

          // 添加内置对象建议
          const builtinSuggestions = getBuiltinSuggestions();
          suggestions.push(...builtinSuggestions);

          // 添加函数建议
          const functionSuggestions = getFunctionSuggestions();
          suggestions.push(...functionSuggestions);
        }
      }

      // 去重并返回建议
      const deduplicatedSuggestions = deduplicateSuggestions(suggestions);
      return {
        suggestions: deduplicatedSuggestions,
      };
    }

    // 对于其他情况，返回空建议，让默认的 JavaScript 智能提示处理
    return {
      suggestions: [],
    };
  },
});
